<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Lead;
use App\Models\Quotation;
use App\Models\Order;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class TaskController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Tasks')->only(['index', 'dashboard']);
        $this->middleware('permission:Create Tasks')->only(['create', 'store']);
        $this->middleware('permission:Edit Tasks')->only(['edit', 'update']);
        $this->middleware('permission:Delete Tasks')->only('destroy');
    }

    public function index(Request $request)
    {
        $query = Task::with(['assignedTo', 'createdBy'])
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->type, fn($q) => $q->where('type', $request->type))
            ->when($request->priority, fn($q) => $q->where('priority', $request->priority))
            ->when($request->assigned_to, fn($q) => $q->where('assigned_to', $request->assigned_to));

        // Filter by user role (assuming role_id 1 is admin)
        if (auth()->user()->role_id !== 1) {
            $query->where('assigned_to', auth()->id());
        }

        $customStatusOrder = ['pending', 'in_progress', 'completed', 'cancelled'];
        $query->orderByRaw("FIELD(status, '".implode("','", $customStatusOrder)."')")
        ->orderBy('due_date', 'asc');
        $tasks = $query->paginate(15);

        // Get filter options

        $users = User::where(['status' => '1'])->where('role_id', '!=', 1)->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $status = config('constants.taskStatus');
        $types = config('constants.taskTypes');
        $priorities = config('constants.taskPriorities');

        $stats = [
            'total' => Task::count(),
            'pending' => Task::where('status', 'pending')->count(),
            'overdue' => Task::overdue()->count(),
            'due_today' => Task::dueToday()->count(),
        ];

        $permissions = [
            'canCreateTask' => auth()->user()->can('Create Tasks'),
            'canEditTask' => auth()->user()->can('Edit Tasks'),
            'canDeleteTask' => auth()->user()->can('Delete Tasks'),
        ];

        return Inertia::render('Tasks/Index', compact('tasks', 'users', 'stats', 'status', 'types', 'priorities', 'permissions'));
    }

    public function create(Request $request)
    {
        $users = User::where(['status' => '1'])->select('id', DB::raw("CONCAT(first_name, '  ', last_name) as name"))->get();
        // Get lead if specified in URL (for creating task from lead page)
        // $lead = null;
        // if ($request->lead_id) {
        //     $lead = Lead::find($request->lead_id);
        // }
        $leads = Lead::where('status', '!=', 'won')->where('status', '!=', 'lost')->select('id', DB::raw("CONCAT(lead_number, ' - ', client_name) as name"))->get();
        $types = config('constants.types');
        $priority = config('constants.priorities');

        return Inertia::render('Tasks/Create', compact('users', 'types', 'priority', 'leads'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:call,follow_up,meeting,email,quote_follow_up,order_follow_up,general,reminder',
            'priority' => 'required|in:low,medium,high,urgent',
            'due_date' => 'required|date|after:now',
            // 'reminder_date' => 'nullable|date|before:due_date|required_if:type,reminder',
            'assigned_to' => 'required|exists:users,id',
            'lead_id' => 'nullable|exists:leads,id',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $task = Task::create(array_merge($validated, [
                'created_by' => auth()->id(),
            ]));

            // Create notification for assigned user
            if ($task->assigned_to !== auth()->id()) {
                NotificationService::createTaskAssigned($task);
            }

            DB::commit();
            return Redirect::route('tasks.index')->with('success', 'Task created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->with('error', 'Failed to create task: ' . $e->getMessage());
        }
    }

    public function show(Task $task)
    {
        $task->load(['assignedTo', 'createdBy', 'lead.county']);

        // Check authorization
        if (auth()->user()->role_id !== 1 && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        return Inertia::render('Tasks/Show', compact('task'));
    }

    public function edit(Task $task)
    {
        // Check authorization
        if (auth()->user()->role_id !== 1 && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }
        $users = User::where(['status' => '1'])->select('id', DB::raw("CONCAT(first_name, '  ', last_name) as name"))->get();
        $leads = Lead::where('status', '!=', 'won')->where('status', '!=', 'lost')->select('id', DB::raw("CONCAT(lead_number, ' - ', client_name) as name"))->get();
        $types = config('constants.types');
        $priority = config('constants.priorities');
        $status = config('constants.status');
        return Inertia::render('Tasks/Edit', compact('task', 'users', 'leads', 'types', 'priority', 'status'));
    }

    public function update(Request $request, Task $task)
    {
        // Check authorization
        if (auth()->user()->role_id !== 1 && $task->assigned_to !== auth()->id()) {
            return Redirect::route('tasks.index')->with('error', 'Unauthorized access');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:call,follow_up,meeting,email,quote_follow_up,order_follow_up,general,reminder',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'due_date' => 'required|date',
            // 'reminder_date' => 'nullable|date|before:due_date|required_if:type,reminder',
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $oldStatus = $task->status;
            $task->update($validated);

            // If status changed to completed, set completed_at
            if ($validated['status'] === 'completed' && $oldStatus !== 'completed') {
                $task->update(['completed_at' => now()]);
            }

            // Status change completed

            DB::commit();
            return Redirect::route('tasks.show', $task)->with('success', 'Task updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', 'Failed to update task: ' . $e->getMessage());
        }
    }

    public function complete(Task $task)
    {
        // Check authorization
        if (auth()->user()->role_id !== 1 && $task->assigned_to !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $task->markAsCompleted();
        return Redirect::back()->with('success', 'Task completed successfully');
    }

    public function destroy(string $id)
    {
        $task = Task::findOrFail($id);
        $task->delete();
        return Redirect::route('tasks.index')->with('success', 'Task deleted successfully');
    }

    public function dashboard()
    {
        $userId = auth()->id();
        $isAdmin = auth()->user()->role_id === 1;

        // Get tasks based on role
        $tasksQuery = $isAdmin ? Task::query() : Task::where('assigned_to', $userId);

        $stats = [
            'total_tasks' => $tasksQuery->count(),
            'pending_tasks' => (clone $tasksQuery)->where('status', 'pending')->count(),
            'overdue_tasks' => (clone $tasksQuery)->overdue()->count(),
            'due_today' => (clone $tasksQuery)->dueToday()->count(),
            'completed_this_week' => (clone $tasksQuery)->where('status', 'completed')
                ->whereBetween('completed_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ];

        // Recent tasks
        $recentTasks = (clone $tasksQuery)->with(['assignedTo', 'lead'])
            ->orderBy('created_at', 'desc')->limit(5)->get();

        // Upcoming tasks
        $upcomingTasks = (clone $tasksQuery)->with(['assignedTo', 'lead'])
            ->where('status', 'pending')
            ->orderBy('due_date', 'asc')->limit(5)->get();

        return Inertia::render('Tasks/Dashboard', compact('stats', 'recentTasks', 'upcomingTasks'));
    }
}
