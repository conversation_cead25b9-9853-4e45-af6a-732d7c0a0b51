<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use App\Models\Lead;
use App\Models\Task;
use App\Models\User;
use App\Models\Order;
use App\Models\Quotation;
use Illuminate\Support\Facades\DB;


class DashboardController extends Controller
{

    public function dashboard(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $permissions = auth()->user()->getAllPermissions()->pluck('name');
        $permissions = json_encode($permissions);

        // Check if user is admin (using Spatie permission system for consistency)
        $isAdmin = auth()->user()->hasRole('Admin');
        $userId = auth()->id();

        // Recent orders - filtered by role
        $recentOrdersQuery = Order::with(['county', 'creator', 'quotation']);
        if (!$isAdmin) {
            $recentOrdersQuery->where('created_by', $userId);
        }
        $recentOrders = $recentOrdersQuery->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Order statistics - filtered by role
        $orderStatsQuery = $isAdmin ? Order::query() : Order::where('created_by', $userId);
        $orderStats = [
            'total' => (clone $orderStatsQuery)->count(),
            'confirmed' => (clone $orderStatsQuery)->where('status', 'confirmed')->count(),
            'under_production' => (clone $orderStatsQuery)->where('status', 'under_production')->count(),
            'shipped' => (clone $orderStatsQuery)->where('status', 'shipped')->count(),
            'delivered' => (clone $orderStatsQuery)->where('status', 'delivered')->count(),
        ];

        // Country-specific revenue stats - filtered by role
        $baseOrderQuery = $isAdmin ? Order::query() : Order::where('created_by', $userId);
        $countryRevenue = [
            'total' => [
                'uk' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United Kingdom%');
                })->sum('total_amount'),

                'us' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United States%');
                })->sum('total_amount'),

                'canada' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Canada%');
                })->sum('total_amount'),

                'australia' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Australia%');
                })->sum('total_amount'),
            ],
            'monthly' => [
                'uk' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United Kingdom%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'us' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%United States%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'canada' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Canada%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),

                'australia' => (clone $baseOrderQuery)->whereHas('lead.county', function($q) {
                    $q->where('name', 'like', '%Australia%');
                })->where('status', '!=', 'pending')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
            ]
        ];

        // Lead statistics - filtered by role
        $leadStatsQuery = $isAdmin ? Lead::query() : Lead::where('created_by', $userId);
        $leadStats = [
            'total' => (clone $leadStatsQuery)->count(),
            'new' => (clone $leadStatsQuery)->where('status', 'new')->count(),
            'contacted' => (clone $leadStatsQuery)->where('status', 'contacted')->count(),
            'quotation' => (clone $leadStatsQuery)->where('status', 'quotation')->count(),
            'won' => (clone $leadStatsQuery)->where('status', 'won')->count(),
        ];

        // Quotation statistics - filtered by role
        $quotationStatsQuery = $isAdmin ? Quotation::query() : Quotation::where('created_by', $userId);
        $quotationStats = [
            'total' => (clone $quotationStatsQuery)->count(),
            'pending' => (clone $quotationStatsQuery)->where('status', 'pending')->count(),
            'quotation_ready' => (clone $quotationStatsQuery)->where('status', 'quotation_ready')->count(),
            'order_placed' => (clone $quotationStatsQuery)->where('status', 'order_placed')->count()
        ];

        // Task statistics - filtered by role (same logic as TaskController)
        $taskStatsQuery = $isAdmin ? Task::query() : Task::where('assigned_to', $userId);
        $taskStats = [
            'total' => (clone $taskStatsQuery)->count(),
            'pending' => (clone $taskStatsQuery)->where('status', 'pending')->count(),
            'overdue' => (clone $taskStatsQuery)->overdue()->count(),
            'due_today' => (clone $taskStatsQuery)->dueToday()->count(),
        ];

        // Monthly trends (last 6 months) - filtered by role
        $monthlyTrends = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);

            $leadsQuery = $isAdmin ? Lead::query() : Lead::where('created_by', $userId);
            $quotationsQuery = $isAdmin ? Quotation::query() : Quotation::where('created_by', $userId);
            $ordersQuery = $isAdmin ? Order::query() : Order::where('created_by', $userId);

            $monthlyTrends[] = [
                'month' => $date->format('M Y'),
                'leads' => (clone $leadsQuery)->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'quotations' => (clone $quotationsQuery)->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'orders' => (clone $ordersQuery)->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)->count(),
                'revenue' => (clone $ordersQuery)->where('status', '!=', 'pending')
                    ->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)
                    ->sum('total_amount'),
            ];
        }

        // Top performing agents - admins see all, regular users see only themselves
        if ($isAdmin) {
            $topAgents = User::where(['status' => '1'])
                ->withCount(['leads', 'quotations', 'orders'])
                ->withSum('orders', 'total_amount')
                ->orderBy('orders_sum_total_amount', 'desc')
                ->limit(5)
                ->get();
        } else {
            // For non-admin users, show only their own stats
            $topAgents = User::where(['status' => '1', 'id' => $userId])
                ->withCount(['leads', 'quotations', 'orders'])
                ->withSum('orders', 'total_amount')
                ->get();
        }

        // Recent activities (last 20) - filtered by role
        $recentActivities = collect();

        // Recent leads - filtered by role
        $recentLeadsQuery = $isAdmin ? Lead::with('creator') : Lead::with('creator')->where('created_by', $userId);
        $recentLeads = $recentLeadsQuery->latest()
            ->limit(5)
            ->get()
            ->map(function($lead) {
                return [
                    'type' => 'lead',
                    'title' => "New lead: {$lead->client_name}",
                    'description' => "Lead {$lead->lead_number} created",
                    'user' => $lead->creator->first_name ?? 'System',
                    'created_at' => $lead->created_at,
                    'icon' => 'user-plus',
                    'color' => 'blue'
                ];
            });

        // Recent quotations - filtered by role
        $recentQuotationsQuery = $isAdmin ? Quotation::with('creator') : Quotation::with('creator')->where('created_by', $userId);
        $recentQuotations = $recentQuotationsQuery->latest()
            ->limit(5)
            ->get()
            ->map(function($quotation) {
                return [
                    'type' => 'quotation',
                    'title' => "Quotation sent: {$quotation->client_name}",
                    'description' => "Quotation {$quotation->quotation_number} created",
                    'user' => $quotation->creator->first_name ?? 'System',
                    'created_at' => $quotation->created_at,
                    'icon' => 'document-text',
                    'color' => 'green'
                ];
            });

        // Recent orders - filtered by role
        $recentOrderActivitiesQuery = $isAdmin ? Order::with('creator') : Order::with('creator')->where('created_by', $userId);
        $recentOrderActivities = $recentOrderActivitiesQuery->latest()
            ->limit(5)
            ->get()
            ->map(function($order) {
                return [
                    'type' => 'order',
                    'title' => "Order created: {$order->client_name}",
                    'description' => "Order {$order->order_number} - {$order->status}",
                    'user' => $order->creator->first_name ?? 'System',
                    'created_at' => $order->created_at,
                    'icon' => 'shopping-cart',
                    'color' => 'purple'
                ];
            });

        $recentActivities = $recentLeads
            ->concat($recentQuotations)
            ->concat($recentOrderActivities)
            ->sortByDesc('created_at')
            ->take(15)
            ->values();

        return Inertia::render('Dashboard', [
            'permissions' => $permissions,
            'recentOrders' => $recentOrders,
            'orderStats' => $orderStats,
            'taskStats' => $taskStats,
            'countryRevenue' => $countryRevenue,
            'leadStats' => $leadStats,
            'quotationStats' => $quotationStats,
            'monthlyTrends' => $monthlyTrends,
            'topAgents' => $topAgents,
            'recentActivities' => $recentActivities,
        ]);
    }
}
