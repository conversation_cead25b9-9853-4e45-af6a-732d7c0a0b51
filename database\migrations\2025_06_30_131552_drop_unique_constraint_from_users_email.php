<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropUnique('users_email_unique'); // Drops the unique index
        });

        Schema::table('leads', function (Blueprint $table) {
            $table->dropUnique('leads_email_unique'); // Drops the unique index
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unique('email'); // Adds it back in case you rollback
        });

        Schema::table('leads', function (Blueprint $table) {
            $table->unique('email'); // Adds it back in case you rollback
        });
    }
};
