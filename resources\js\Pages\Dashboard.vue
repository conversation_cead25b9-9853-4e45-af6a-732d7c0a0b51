<script setup>

import AdminLayout from "@/Layouts/AdminLayout.vue";
import { onBeforeMount, ref, computed } from "vue";
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import Bar<PERSON>hart from '@/Components/BarChart.vue';
import Pie<PERSON>hart from '@/Components/PieChart.vue';
import MultiLineChart from '@/Components/MultiLineChart.vue';
import Pagination from '@/Components/Pagination.vue';
import { Head , useForm} from '@inertiajs/vue3';
import ActionLink from '@/Components/ActionLink.vue';

const form = useForm({});

const props = defineProps({
    recentOrders: Array,
    orderStats: Object,
    countryRevenue: Object,
    leadStats: Object,
    quotationStats: Object,
    monthlyTrends: Array,
    topAgents: Array,
    recentActivities: Array,
    permissions: Object,
    taskStats: Object
});

onBeforeMount(async () => {
    localStorage.setItem("permissions", JSON.stringify(props.permissions));
});

const formatCurrency = (amount, countyName = 'United Kingdom') => {
    const countyCurrencyMap = {
        'United Kingdom': { locale: 'en-GB', currency: 'GBP' },
        'United States': { locale: 'en-US', currency: 'USD' },
        'Canada': { locale: 'en-CA', currency: 'CAD' },
        'Australia': { locale: 'en-AU', currency: 'AUD' }
    };

    const matchedKey = Object.keys(countyCurrencyMap).find(key =>
        countyName?.toLowerCase().includes(key.toLowerCase())
    );

    const { locale, currency } = countyCurrencyMap[matchedKey] || countyCurrencyMap['United Kingdom'];

    const formattedAmount = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        currencyDisplay: 'symbol'
    }).format(amount);

    return `${currency} ${formattedAmount}`;
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getActivityIcon = (type) => {
    const icons = {
        'lead': 'M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z',
        'quotation': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
        'order': 'M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6'
    };
    return icons[type] || icons.lead;
};

const getActivityColor = (type) => {
    const colors = {
        'lead': 'text-blue-600 bg-blue-100',
        'quotation': 'text-green-600 bg-green-100',
        'order': 'text-purple-600 bg-purple-100'
    };
    return colors[type] || colors.lead;
};

// Calculate conversion rates
const conversionRates = computed(() => {
    const leadToQuotation = props.leadStats.total > 0 ?
        ((props.quotationStats.total / props.leadStats.total) * 100).toFixed(1) : 0;
    const quotationToOrder = props.quotationStats.total > 0 ?
        ((props.orderStats.total / props.quotationStats.total) * 100).toFixed(1) : 0;
    const leadToOrder = props.leadStats.total > 0 ?
        ((props.orderStats.total / props.leadStats.total) * 100).toFixed(1) : 0;

    return { leadToQuotation, quotationToOrder, leadToOrder };
});
</script>

<template>
    <Head title="Dashboard" />

    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                    <p class="text-gray-600 mt-1">Complete overview of your business performance</p>
                </div>
            </div>

            <!-- Key Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

                <!-- Total Leads -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Leads</dt>
                                    <dd class="text-lg font-semibold text-gray-900">{{ leadStats.total }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Orders -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                                    <dd class="text-lg font-semibold text-gray-900">{{ orderStats.total }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Revenue -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Quotations</dt>
                                    <dd class="text-lg font-semibold text-gray-900">{{ quotationStats.total }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Revenue -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Task</dt>
                                    <dd class="text-lg font-semibold text-gray-900">{{ taskStats.total }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>




            </div>

            <!-- Country-Specific Revenue -->
            <div class="bg-white shadow rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Revenue by Country</h3>

                <!-- Total Revenue by Country -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Total Sales Revenue</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-blue-600">UK Sales</p>
                                    <p class="text-lg font-bold text-blue-900">{{ formatCurrency(countryRevenue.total.uk, 'United Kingdom') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-600">US Sales</p>
                                    <p class="text-lg font-bold text-green-900">{{ formatCurrency(countryRevenue.total.us, 'United States') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-purple-600">Canada Sales</p>
                                    <p class="text-lg font-bold text-purple-900">{{ formatCurrency(countryRevenue.total.canada, 'Canada') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-600">Australia Sales</p>
                                    <p class="text-lg font-bold text-yellow-900">{{ formatCurrency(countryRevenue.total.australia, 'Australia') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- This Month Revenue by Country -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">This Month Sales Revenue</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-blue-600">UK This Month</p>
                                    <p class="text-lg font-bold text-blue-900">{{ formatCurrency(countryRevenue.monthly.uk, 'United Kingdom') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-600">US This Month</p>
                                    <p class="text-lg font-bold text-green-900">{{ formatCurrency(countryRevenue.monthly.us, 'United States') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-purple-600">Canada This Month</p>
                                    <p class="text-lg font-bold text-purple-900">{{ formatCurrency(countryRevenue.monthly.canada, 'Canada') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="text-2xl"></span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-600">Australia This Month</p>
                                    <p class="text-lg font-bold text-yellow-900">{{ formatCurrency(countryRevenue.monthly.australia, 'Australia') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conversion Rates -->
            <div class="bg-white shadow rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Conversion Rates</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ conversionRates.leadToQuotation }}%</div>
                        <div class="text-sm text-gray-500">Lead → Quotation</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ conversionRates.quotationToOrder }}%</div>
                        <div class="text-sm text-gray-500">Quotation → Order</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ conversionRates.leadToOrder }}%</div>
                        <div class="text-sm text-gray-500">Lead → Order</div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Pipeline Status -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Sales Pipeline</h3>
                    <div class="space-y-4">
                        <!-- Leads -->
                        <div>
                            <div class="flex justify-between text-sm font-medium text-gray-900 mb-1">
                                <span>Leads</span>
                                <span>{{ leadStats.total }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${leadStats.total > 0 ? (leadStats.won / leadStats.total) * 100 : 0}%`"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>New: {{ leadStats.new }}</span>
                                <span>Won: {{ leadStats.won }}</span>
                            </div>
                        </div>

                        <!-- Quotations -->
                        <div>
                            <div class="flex justify-between text-sm font-medium text-gray-900 mb-1">
                                <span>Quotations</span>
                                <span>{{ quotationStats.total }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" :style="`width: ${quotationStats.total > 0 ? (quotationStats.total / Math.max(leadStats.total, 1)) * 100 : 0}%`"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Pending: {{ quotationStats.pending }}</span>
                                <span>Order Placed: {{ quotationStats.order_placed }}</span>
                            </div>
                        </div>

                        <!-- Orders -->
                        <div>
                            <div class="flex justify-between text-sm font-medium text-gray-900 mb-1">
                                <span>Orders</span>
                                <span>{{ orderStats.total }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" :style="`width: ${orderStats.total > 0 ? (orderStats.total / Math.max(leadStats.total, 1)) * 100 : 0}%`"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Confirmed: {{ orderStats.confirmed }}</span>
                                <span>Delivered: {{ orderStats.delivered }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Status Distribution -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status Distribution</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">Confirmed</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ orderStats.confirmed }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">Under Production</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ orderStats.under_production }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-indigo-400 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">Shipped</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ orderStats.shipped }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">Delivered</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ orderStats.delivered }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Recent Activities -->
                <div class="lg:col-span-2 bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                    <div class="flow-root">
                        <ul class="-mb-8">
                            <li v-for="(activity, index) in recentActivities" :key="index" class="relative pb-8">
                                <div v-if="index !== recentActivities.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span :class="['h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white', getActivityColor(activity.type)]">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getActivityIcon(activity.type)"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-900">{{ activity.title }}</p>
                                            <p class="text-sm text-gray-500">{{ activity.description }}</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time>{{ formatDate(activity.created_at) }}</time>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Top Performers -->
                <!-- <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Agents</h3>
                    <div class="space-y-4">
                        <div v-for="agent in topAgents" :key="agent.id" class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ agent.first_name }} {{ agent.last_name }}</p>
                                <p class="text-xs text-gray-500">{{ agent.orders_count }} orders</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-green-600">{{ formatCurrency(agent.orders_sum_total_amount) }}</p>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </AdminLayout>
</template>
