<script setup>
import { ref, onMounted, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Dropdown from '@/Components/Dropdown.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import CommentsCard from '@/Components/CommentsCard.vue';
import LeadComments from '@/Components/LeadComments.vue';
import { Head, useForm , Link, router} from '@inertiajs/vue3';
import { sortAndSearch } from '@/Composables/sortAndSearch';

const props = defineProps(['data', 'search', 'permissions', 'counties', 'county_id', 'agents', 'agent_id', 'status', 'isAdmin']);
const { form, search, sort, fetchData, sortKey, sortDirection } = sortAndSearch('leads.index');

const modalVisible = ref(false);
const selectedLeadId = ref(null);

// View toggle state
const viewMode = ref('card'); // 'card' or 'table'

// Status options for inline editing
const statusOptions = [
    { id: 'new', name: 'New' },
    { id: 'contacted', name: 'Contacted' },
    { id: 'quotation', name: 'Quotation' },
    { id: 'negotiation', name: 'Negotiation' },
    { id: 'won', name: 'Won' },
];

// Enhanced filter options with "All" option
const agentsWithAll = computed(() => {
    return [
        { id: '', name: 'All Agents' },
        ...props.agents
    ];
});

const countiesWithAll = computed(() => {
    return [
        { id: '', name: 'All Country' },
        ...props.counties
    ];
});

const statusWithAll = computed(() => {
    return [
        { id: '', name: 'All Status' },
        ...statusOptions
    ];
});

const columns = [
    { field: 'lead_number', label: 'LEAD NO', sortable: true , visible: true},
    { field: 'client_name', label: 'CLIENT NAME', sortable: true , visible: true},
    { field: 'county.name', label: 'COUNTRY', sortable: false , visible: true},
    { field: 'open_size', label: 'OPEN SIZE', sortable: true , visible: true},
    { field: 'box_style', label: 'BOX STYLE', sortable: true , visible: true},
    { field: 'email', label: 'Email', sortable: true , visible: true},
    { field: 'number', label: 'Number', sortable: true , visible: true},
    { field: 'status', label: 'STATUS', sortable: true , visible: true},
    { field: 'created_at', label: 'DATE', sortable: true , visible: true},
    { field: 'creator.first_name', label: 'AGENT', sortable: true , visible: props.isAdmin },
    { field: 'action', label: 'ACTION', sortable: false , visible: true }
];

const openDeleteModal = (leadId) => {
    selectedLeadId.value = leadId;
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteLead = () => {
    form.delete(route('leads.destroy', { lead: selectedLeadId.value }), {
        onSuccess: () => closeModal()
    });
};

const getStatusClass = (status) => {
    const classes = {
        'new': 'bg-blue-100 text-blue-800',
        'contacted': 'bg-purple-100 text-purple-800',
        'quotation': 'bg-yellow-100 text-yellow-800',
        'negotiation': 'bg-orange-100 text-orange-800',
        'won': 'bg-green-100 text-green-800',
        'lost': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
};

const agentId = ref(props.agent_id || '');
const countyId = ref(props.county_id || '');
const status = ref(props.status || '');
const searchValue = ref('');

// Inline editing state
const editingStatus = ref({});

const setAgent = (id, name) => {
    agentId.value = id;
    handleSearchChange(searchValue.value, agentId.value , countyId.value , status.value );
};

const setCounty = (id, name) => {
    countyId.value = id;
    handleSearchChange(searchValue.value, agentId.value , countyId.value , status.value );
};

const setStatus = (id, name) => {
    status.value = id;
    handleSearchChange(searchValue.value, agentId.value , countyId.value , status.value );
};

const handleSearchChange = (value, agentId, countyId, status) => {
    searchValue.value = value;
    console.log('agentId', agentId);
    console.log('countyId', countyId);
    console.log('status', status);
    // Convert empty string to null for backend
    const agentParam = agentId === '' ? null : agentId;
    const countyParam = countyId === '' ? null : countyId;

    form.get(route('leads.index', { search: value , agent_id: agentParam, county_id: countyParam, status: status }), {
        preserveState: true,
    });
};

// Inline status editing functions
const startEditingStatus = (leadId, currentStatus) => {
    editingStatus.value[leadId] = currentStatus;
};

const cancelEditingStatus = (leadId) => {
    delete editingStatus.value[leadId];
};

const updateLeadStatus = (leadId, newStatus) => {
    router.post(route('leads.update-status',  leadId), { status: newStatus }, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            delete editingStatus.value[leadId];
            // Get current URL parameters to preserve pagination
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = urlParams.get('page') || 1;

            // Reload with current filters and page preserved
            router.get(route('leads.index'), {
                search: searchValue.value,
                agent_id: agentId.value === '' ? null : agentId.value,
                county_id: countyId.value === '' ? null : countyId.value,
                page: currentPage
            }, {
                preserveScroll: true,
                preserveState: true,
                only: ['data']
            });
        },
        onError: (errors) => {
            console.error("Update failed:", errors);
            alert("Failed to update status. Please try again.");
        }
    });
};

//comments

const commentModal = ref({
    show: false,
    leadId: null,
    comments: []
})

// Comment Modal Methods
const openCommentModal = (lead) => {
    commentModal.value = {
        show: true,
        leadId: lead.id,
        comments: lead.comments || []
    }
}

const closeCommentModal = () => {
    commentModal.value = {
        show: false,
        leadId: null,
        comments: []
    }
}
</script>

<template>
    <Head title="Leads" />
    <AdminLayout>
        <div class="animate-top">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Leads</h1>
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input type="text" v-model="search" @input="handleSearchChange(search, agentId, countyId, status)" class="block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white" placeholder="Search for leads...">
                    </div>

                    <!-- View Toggle -->
                    <div class="flex rounded-lg border border-gray-200 bg-white">
                        <button @click="viewMode = 'card'"
                                :class="['px-3 py-2 text-sm font-medium rounded-l-lg', viewMode === 'card' ? 'bg-indigo-600 text-white' : 'text-gray-700 hover:bg-gray-50']">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'table'"
                                :class="['px-3 py-2 text-sm font-medium rounded-r-lg', viewMode === 'table' ? 'bg-indigo-600 text-white' : 'text-gray-700 hover:bg-gray-50']">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h3M3 20h18a1 1 0 001-1V5a1 1 0 00-1-1H3a1 1 0 00-1 1v14a1 1 0 001 1z"></path>
                            </svg>
                        </button>
                    </div>
                    <div v-if="permissions.canCreateLead" class="sm:flex-none">
                        <CreateButton :href="route('leads.create')">
                            Add Lead
                        </CreateButton>
                    </div>
                </div>
            </div>

            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4" v-if="props.isAdmin">
                        <InputLabel for="agent_filter" value="Agents" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="agentsWithAll"
                            v-model="agentId"
                            @onchange="setAgent"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="county_filter" value="Country" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="countiesWithAll"
                            v-model="countyId"
                            @onchange="setCounty"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="county_filter" value="Status" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="statusWithAll"
                            v-model="status"
                            @onchange="setStatus"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card View -->
            <div class="" v-if="(data.data && data.data.length > 0)">
                <div v-if="viewMode === 'card'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                    <div v-for="lead in data.data" :key="lead.id" class="bg-white p-5 rounded-lg shadow hover:shadow-md transition">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">
                                {{ lead.client_name }}
                                </h3>
                                <p class="text-sm font-semibold text-gray-900">Lead No: {{ lead.lead_number }}</p>

                            </div>
                            <div class="flex-shrink-0">
                                <!-- Status -->
                                <div v-if="editingStatus[lead.id] !== undefined">
                                <select
                                    v-model="editingStatus[lead.id]"
                                    @change="updateLeadStatus(lead.id, editingStatus[lead.id])"
                                    class="text-sm border-gray-300 rounded px-2 py-1 mt-1"
                                >
                                    <option v-for="status in statusOptions" :key="status.id" :value="status.id">
                                    {{ status.name }}
                                    </option>
                                </select>
                                <button @click="cancelEditingStatus(lead.id)" class="text-gray-400 ml-1 hover:text-gray-600">✕</button>
                                </div>
                                <span
                                v-else
                                :class="['px-3 py-1 rounded-full text-xs font-semibold cursor-pointer mt-1 inline-block', getStatusClass(lead.status)]"
                                @click="startEditingStatus(lead.id, lead.status)"
                                title="Click to edit status"
                                >
                                {{ lead.status.charAt(0).toUpperCase() + lead.status.slice(1) }}
                                </span>
                            </div>
                        </div>
                        <div class="flex space-x-2 mt-2 justify-between items-center w-full">
                            <p class="text-xs text-gray-700">Country: {{ lead.county ? lead.county.name : 'N/A' }}</p>
                            <p class="text-xs text-gray-700">Agent: {{ lead.creator?.first_name || 'N/A' }}</p>
                            <p class="text-xs text-gray-700">Date: {{ new Date(lead.created_at).toLocaleDateString('en-GB') }}</p>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm"><strong>Open Size:</strong> {{ lead.open_size }}</p>
                            <p class="text-sm"><strong>Email:</strong> {{ lead.email ?? '-' }}</p>
                            <p class="text-sm"><strong>Number:</strong> {{ lead.number ?? '-' }}</p>
                        </div>
                        <CommentsCard
                            :comments="lead.comments || []"
                            :current-user-id="$page.props.auth.user.id"
                            :is-admin="$page.props.auth.user.role_id === 1"
                            @add-comment="openCommentModal(lead)" />
                        <div class="flex space-x-2 mt-4">
                            <Link :href="route('leads.show', lead.id)"
                                class="flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100">
                                View
                            </Link>
                            <Link :href="route('leads.edit', lead.id)"
                                class="flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100">
                                Edit
                            </Link>
                            <button v-if="permissions.canDeleteLead"
                                @click="openDeleteModal(lead.id)"
                                class="flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <div class="bg-white shadow rounded-lg p-6 mt-8">
                    <div colspan="12" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                        No data found.
                    </div>
                </div>
            </div>

            <div v-if="viewMode === 'table'" class="mt-8 overflow-x-auto rounded-lg max-w-full">
                <div class="shadow rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" v-show="column.visible" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr  class="odd:bg-white even:bg-gray-50 border-b" v-for="lead in data.data" :key="lead.id">
                                <td class="px-4 py-2.5 min-w-28">{{ lead.lead_number }}</td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">{{ lead.client_name }}</td>
                                <td class="px-4 py-2.5">{{ lead.county ? lead.county.name : 'N/A' }}</td>
                                <td class="px-4 py-2.5 min-w-28">{{ lead.open_size }}</td>
                                <td class="px-4 py-2.5 min-w-28">{{ lead.box_style }}</td>
                                <td class="px-4 py-2.5 min-w-32">{{ lead.email ?? '-' }}</td>
                                <td class="px-4 py-2.5 min-w-32">{{ lead.number ?? '-' }}</td>
                                <td class="px-4 py-2.5">
                                    <!-- Inline Status Editing -->
                                    <div v-if="editingStatus[lead.id] !== undefined" class="flex items-center space-x-2 w-full">
                                        <select
                                            v-model="editingStatus[lead.id]"
                                            class="text-sm  border-gray-300 rounded px-2 py-1"
                                            @change="updateLeadStatus(lead.id, editingStatus[lead.id])"
                                        >
                                            <option class="text-sm text-gray-900 text-bold" v-for="status in statusOptions" :key="status.id" :value="status.id">
                                                {{ status.name }}
                                            </option>
                                        </select>
                                        <button
                                            @click="cancelEditingStatus(lead.id)"
                                            class="text-gray-400 hover:text-gray-600 text-sm"
                                            title="Cancel"
                                        >
                                            ✕
                                        </button>
                                    </div>
                                    <!-- Display Status -->
                                    <div v-else class="flex items-center space-x-2">
                                        <span
                                            :class="['px-3 py-1 rounded-full text-sm font-medium cursor-pointer', getStatusClass(lead.status)]"
                                            @click="startEditingStatus(lead.id, lead.status)"
                                            title="Click to edit status"
                                        >
                                            {{ lead.status.charAt(0).toUpperCase() + lead.status.slice(1) }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-4 py-2.5 min-w-28">{{ new Date(lead.created_at).toLocaleDateString('en-GB') }}</td>
                                <td class="px-4 py-2.5" v-if="props.isAdmin">{{ lead.creator ? lead.creator.first_name : 'N/A' }}</td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('leads.show', {id: lead.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">View</span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink :href="route('leads.edit',{id:lead.id})" v-if="permissions.canEditLead">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <!-- <ActionLink :href="route('quotations.convert', {lead: lead.id})" v-if="permissions.canCreateQuotation && lead.status !== 'quotation'">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Convert to Quotation
                                                        </span>
                                                    </template>
                                                </ActionLink> -->
                                                <button type="button" @click="openDeleteModal(lead.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteLead">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="12" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>

        <Modal :show="commentModal.show" @close="closeCommentModal">
            <div class="bg-white rounded-lg p-6 w-full">
                <h3 class="text-lg font-medium mb-4">Add Comment</h3>
                <LeadComments
                    :lead-id="commentModal.leadId"
                    :comments="commentModal.comments"
                    :current-user-id="$page.props.auth.user.id"
                    :is-admin="$page.props.auth.user.role_id === 1"
                    @close="closeCommentModal" />
            </div>
        </Modal>

        <!-- Delete Confirmation Modal -->
        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this lead?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <DangerButton class="ml-3" @click="deleteLead" :disabled="form.processing">
                        Delete Lead
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
