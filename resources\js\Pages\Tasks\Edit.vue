<script setup>
import { ref } from 'vue'
import { Head, Link, useForm } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import TextArea from '@/Components/TextArea.vue'
import InputError from '@/Components/InputError.vue'
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue'
import SvgLink from '@/Components/ActionLink.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'

const props = defineProps({
    task: Object,
    users: Array,
    types: Array,
    priority: Array,
    leads: Array,
    status: Array
})

const form = useForm({
    title: props.task.title || '',
    description: props.task.description || '',
    type: props.task.type || '',
    priority: props.task.priority || 'medium',
    status: props.task.status || 'pending',
    due_date: props.task.due_date ? new Date(props.task.due_date).toISOString().slice(0, 16) : '',
    reminder_date: props.task.reminder_date ? new Date(props.task.reminder_date).toISOString().slice(0, 16) : '',
    assigned_to: props.task.assigned_to || '',
    lead_id: props.task.lead_id || null,
    notes: props.task.notes || ''
})

const setLead = (id, name) => form.lead_id = id
const setType = (id, name) => form.type = id
const setPriority = (id, name) => form.priority = id
const setAssigned = (id, name) => form.assigned_to = id
const setStatus = (id, name) => form.status = id

const postponeTask = () => {
    const due = new Date(form.due_date)
    due.setDate(due.getDate() + 1)
    form.due_date = due.toISOString().slice(0, 16)

    if (form.reminder_date) {
        const reminder = new Date(form.reminder_date)
        reminder.setDate(reminder.getDate() + 1)
        form.reminder_date = reminder.toISOString().slice(0, 16)
    }
}

const submit = () => {
    form.put(route('tasks.update', props.task.id), {
        preserveScroll: true,
    })
}
</script>

<template>
    <Head title="Tasks" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Task</h2>
                    <p class="text-sm text-gray-600 mt-1">Update task details and status</p>
                </div>
                <div class="flex space-x-2">
                    <Link :href="route('tasks.index')"
                            class="px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100">
                        ← Back to Tasks
                    </Link>
                </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
                    <div class="md:col-span-3">
                        <InputLabel for="lead_id" value="Lead" />
                        <div class="relative mt-2">
                        <SearchableDropdownNew :options="props.leads" v-model="form.lead_id" @onchange="setLead" />
                        </div>
                        <InputError :message="form.errors.lead_id" />
                    </div>

                    <div class="md:col-span-3">
                        <InputLabel for="title" value="Task Title *" />
                        <TextInput id="title" v-model="form.title" type="text" required class="mt-1 block w-full" />
                        <InputError :message="form.errors.title" />
                    </div>

                    <div class="md:col-span-3">
                        <InputLabel for="type" value="Task Type *" />
                        <div class="relative mt-2">
                        <SearchableDropdownNew :options="props.types" v-model="form.type" @onchange="setType" />
                        </div>
                        <InputError :message="form.errors.type" />
                    </div>

                    <div class="md:col-span-3">
                        <InputLabel for="priority" value="Priority *" />
                        <div class="relative mt-2">
                        <SearchableDropdownNew :options="props.priority" v-model="form.priority" @onchange="setPriority" />
                        </div>
                        <InputError :message="form.errors.priority" />
                    </div>

                    <div class="md:col-span-6">
                        <InputLabel for="description" value="Description" />
                        <TextArea id="description" v-model="form.description" :rows="3" />
                        <InputError :message="form.errors.description" />
                    </div>

                    <div class="md:col-span-6">
                        <InputLabel for="notes" value="Additional Notes" />
                        <TextArea id="notes" v-model="form.notes" :rows="3" />
                        <InputError :message="form.errors.notes" />
                    </div>

                    <div class="md:col-span-4">
                        <InputLabel for="due_date" value="Due Date & Time *" />
                        <TextInput id="due_date" type="datetime-local" v-model="form.due_date" required class="mt-1 block w-full" />
                        <InputError :message="form.errors.due_date" />
                    </div>

                    <!-- Reminder Date - Show only for reminder type tasks -->
                    <div v-if="form.type === 'reminder'" class="md:col-span-4">
                        <InputLabel for="reminder_date" value="Reminder Date & Time *" />
                        <TextInput
                            id="reminder_date"
                            type="datetime-local"
                            v-model="form.reminder_date"
                            class="mt-1 block w-full"
                            :required="form.type === 'reminder'"
                        />
                        <InputError :message="form.errors.reminder_date" />
                        <p class="text-xs text-gray-500 mt-1">When should you be reminded about this task?</p>
                    </div>

                    <div class="md:col-span-4">
                        <InputLabel for="assigned_to" value="Assign To *" />
                        <div class="relative mt-2">
                        <SearchableDropdownNew :options="props.users" v-model="form.assigned_to" @onchange="setAssigned" />
                        </div>
                        <InputError :message="form.errors.assigned_to" />
                    </div>

                    <div class="md:col-span-4">
                        <InputLabel for="status" value="Status *" />
                        <div class="relative mt-2">
                        <SearchableDropdownNew :options="props.status" v-model="form.status" @onchange="setStatus" />
                        </div>
                        <InputError :message="form.errors.status" />
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Status Updates:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button type="button" @click="form.status = 'in_progress'" class="px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">🔄 Start Working</button>
                        <button type="button" @click="form.status = 'completed'" class="px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">✅ Mark Complete</button>
                        <button type="button" @click="postponeTask" class="px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200">⏰ Postpone +1 Day</button>
                        <button type="button" @click="form.status = 'cancelled'" class="px-3 py-2 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200">❌ Cancel Task</button>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('tasks.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                            </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">
                            <span v-if="form.processing">Updating...</span>
                            <span v-else>Update</span>
                        </PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
